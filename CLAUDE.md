# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This repository contains a collection of Chinese light novels/web novels stored as Markdown files. The content includes:

- `我穿书了，但书里没有我这个角色.md` - A transmigration/isekai novel
- `18岁少爷重振家族荣耀.md` - A business/romance novel 
- `武器为尊：废材的逆袭之路.md` - A cultivation/martial arts novel

## File Structure

All novels are stored as single markdown (.md) files with Chinese filenames. The files are structured with:
- Main title as H1 header
- Chapter titles as H2 headers
- Standard markdown paragraph formatting
- Line numbers for reference (1→, 2→, etc.)

## Working with These Files

### Reading and Analysis
- Files are in UTF-8 encoding with Chinese characters
- Content is creative fiction with adult themes
- Each file is quite large (1000+ lines)
- Use the Read tool with limit parameters for large files

### File Operations
- Files can be read using standard file tools
- Use absolute paths when referencing files
- Directory name contains Chinese characters - use proper escaping if needed

## Important Notes

- This is creative content, not programming code
- Files contain adult-oriented fiction content
- Content modification should be approached carefully
- Focus on technical file operations rather than content enhancement

## Common Tasks

When working with these files, common operations might include:
- Reading specific sections or chapters
- Text analysis and statistics
- File format conversion
- Searching for specific content patterns
- Backup and organization tasks

Avoid content generation or story enhancement tasks - focus on technical file management and analysis operations only.